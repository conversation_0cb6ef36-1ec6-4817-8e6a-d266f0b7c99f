
namespace: 54dev

# PHP configuration for production-like environment
php:
  environment: production
  settings:
    display_errors: "Off"
    display_startup_errors: "Off"
    error_reporting: "E_ALL & ~E_DEPRECATED & ~E_STRICT"
    log_errors: "On"
  debug:
    enabled: false
    xdebug:
      enabled: false

admin:
  paymentOwnerSystemTitle: "ООО «ПодКонтролем»"
  telegram:
    failSend: true
    token: ""
    failChatId: ""
  lkp:
    endpoint: localhost:5000
    enabled: false
    logLevel: 2
    ssl: true
    updateStatus: 60000
  regionId: "0"
  mapProvider: yandex
  language: ru


# MySQL configuration
mysql:
  host: db54dev-node1.tkp2.prod
  port: 3306
  database: sdbp54kubeprod
  username: gds-user
  password: ""
  socket: ""
  debug: true

# Redis configuration
redis:
  host: redis.ext.svc.cluster.local
#  host: ************
  port: 6379
  database: 0
  password: ""


# Cache configuration
cache:
  prefix: sdbp_54dev-kube

api:
  gdsHost: 'api.sdbp.54dev-kube.tkp2.prod/'
  # API Cache configuration
  cache:
    redis:
      prefix: "sdbp-54dev-kube_api_"

# API-Mobile module configuration
apiMobile:
  cache:
    prefix: "sdbp_54dev-kube_api_mobile"

# Instance configuration
instance:
  prefix: "054dev:sdbp:1"

# Cron jobs configuration for 54dev
cronJobs:
#  - name: load-terminal-data
#    schedule: "*/8 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=load_all_terminal_data
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --forks=1
#
#  - name: parse-transactions
#    schedule: "*/1 * * * *"
#    args:
#      - ./api-mobile/cli/cron_parse_transactions.php
#      - --http_host=api-mobile.sdbp.54dev-kube.tkp2.prod

#  - name: bill-emv-transaction
#    schedule: "*/1 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=bill_emv_transaction
#      - --max_count=5000
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --forks=1
#      - --ordered=1
#
#  - name: check-emv-abonements
#    schedule: "*/1 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=check_emv_abonements
#      - --max_count=5000
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --forks=1

  - name: recalculate-stoplist
    schedule: "*/20 * * * *"
    args:
      - ./admin/cli/cron_recalculate_stoplist.php
      - --http_host=admin.sdbp.54dev-kube.tkp2.prod
      - --time_limit=19 minutes

  - name: emission-import
    schedule: "*/10 * * * *"
    args:
      - ./admin/cli/cron_emission_import.php
      - --http_host=admin.sdbp.54dev-kube.tkp2.prod

#  - name: reload-city
#    schedule: "*/21 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=reload_city
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --forks=1

#  - name: ride-all-short
#    schedule: "*/5 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=ride_all
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --date_end_shift=0
#      - --forks=1
#
#  - name: ride-all-long
#    schedule: "*/30 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=ride_all
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --date_end_shift=2
#      - --forks=1
#
#  - name: waypoints
#    schedule: "*/15 * * * *"
#    args:
#      - ./api/cli/api_action.php
#      - --action=waypoints
#      - --partner_id=1
#      - --http_host=api.sdbp.54dev-kube.tkp2.prod
#      - --forks=1
