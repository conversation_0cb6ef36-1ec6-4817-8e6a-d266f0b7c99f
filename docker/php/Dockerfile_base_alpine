FROM php:7.4.33-fpm-alpine3.16

# Установка системных зависимостей (разделены на build и runtime)
RUN apk add --no-cache --update \
    # Runtime зависимости
    git \
    wget \
    zip \
    unzip \
    libmemcached \
    libzip \
    libpng \
    libjpeg-turbo \
    freetype \
    && apk add --no-cache --virtual .build-deps \
    # Build зависимости
    zlib-dev \
    libzip-dev \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    libmemcached-dev \
    cyrus-sasl-dev \
    g++ \
    make \
    autoconf \
    libtool \
    linux-headers

# Установка основных PHP расширений
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
    gd \
    mysqli \
    pdo \
    pdo_mysql \
    zip \
    sockets \
    pcntl \
    opcache

RUN pecl install redis-5.3.7 \
    && docker-php-ext-enable redis \
    && rm -rf /tmp/pear

RUN pecl install memcached-3.1.5 \
    && docker-php-ext-enable memcached \
    && rm -rf /tmp/pear

# Установка gRPC
RUN apk add --no-cache cmake re2c \
    && pecl install grpc-1.41.0 \
    && docker-php-ext-enable grpc \
    && rm -rf /tmp/pear

# Установка php-stribog с патчем
COPY ./docker/php/patch/gost3411-2012.c /tmp/
RUN git clone https://github.com/sjinks/php-stribog /usr/src/php/ext/stribog \
    && cd /usr/src/php/ext/stribog \
    && cp /tmp/gost3411-2012.c ./ \
    && phpize \
    && ./configure \
    && make \
    && make install \
    && docker-php-ext-enable stribog \
    && make test

    # Очистка build-зависимостей
RUN apk del .build-deps \
        && rm -rf /tmp/* /var/cache/apk/*
